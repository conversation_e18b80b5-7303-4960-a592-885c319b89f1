<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\Author\app\Repositories\AuthorRepository;

class AuthorController extends Controller
{
    protected $authorRepository;

    public function __construct(
        AuthorRepository $authorRepository
    ) {
        $this->authorRepository = $authorRepository;
    }
    /**
     * Display all authors with pagination and filtering.
     */
    public function index(): View
    {

        return view('frontend.authors.index', [
           
        ]);
    }

    /**
     * Display a specific author profile.
     */
    public function show(string $authorSlug): View
    {
        // Get author with their books
        // $author = Author::with(['books' => function ($query) {
        //     $query->where('status', 'published')->latest();
        // }])->where('slug', $authorSlug)->where('status', 'active')->firstOrFail();

        return view('frontend.authors.show', [
            // 'author' => $author,
        ]);
    }
}
