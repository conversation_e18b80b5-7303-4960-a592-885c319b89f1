<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\Testimonial\app\Repositories\TestimonialRepository;

class TestimonialController extends Controller
{
    protected $testimonialRepository;

    public function __construct(
        TestimonialRepository $testimonialRepository
    ) {
        $this->testimonialRepository = $testimonialRepository;
    }
    /**
     * Display all services.
     */
    public function index(): View
    {
        return view('frontend.testimonials.index');
    }
}
